import { getAllPages, getAllPosts, getContentStats } from '@/lib/content';
import { BlogPost, Page } from '@/types/content';
import { NextResponse } from 'next/server';

/**
 * 创建内容示例对象
 */
function createContentExample(content: BlogPost[]) {
  if (content.length === 0) return null;

  return {
    title: content[0].metadata.title,
    slug: content[0].metadata.slug,
    publishedAt: content[0].metadata.publishedAt,
  };
}

/**
 * 创建页面示例对象
 */
function createPageExample(pages: Page[]) {
  if (pages.length === 0) return null;

  return {
    title: pages[0].metadata.title,
    slug: pages[0].metadata.slug,
  };
}

export function GET() {
  try {
    // Test content management system
    const enPosts = getAllPosts('en');
    const zhPosts = getAllPosts('zh');
    const enPages = getAllPages('en');
    const zhPages = getAllPages('zh');
    const stats = getContentStats();

    const result = {
      success: true,
      message: 'Content management system is working!',
      data: {
        posts: {
          en: enPosts.length,
          zh: zhPosts.length,
          total: enPosts.length + zhPosts.length,
          examples: {
            en: createContentExample(enPosts),
            zh: createContentExample(zhPosts),
          }
        },
        pages: {
          en: enPages.length,
          zh: zhPages.length,
          total: enPages.length + zhPages.length,
          examples: {
            en: createPageExample(enPages),
            zh: createPageExample(zhPages),
          }
        },
        stats,
        features: {
          mdxParsing: true,
          frontmatterValidation: true,
          multiLanguageSupport: true,
          typeScriptTypes: true,
          contentValidation: true,
          gitBasedWorkflow: true,
        }
      }
    };

    return NextResponse.json(result);
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Content test error:', error);
    }
    return NextResponse.json(
      {
        success: false,
        message: 'Content management system test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
