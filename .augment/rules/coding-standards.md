---
type: 'always_apply'
description: 'Core coding standards that apply to all code in this project'
---

# Core Coding Standards

Always use explicit TypeScript types instead of 'any'. Keep functions under 80
lines. Validate all user inputs and sanitize file paths. Use React Server
Components by default in Next.js 15.

For security, prevent object injection attacks by validating property names
against whitelists. Always normalize file paths and check for directory
traversal attempts.

Use structured logging instead of console statements. Prefer dot notation for
property access. Define constants instead of magic numbers.

## Code Formatting Standards

Always use single quotes for strings. Include trailing commas in all multi-line
structures (objects, arrays, function parameters). Limit line width to 80
characters. Use 2 spaces for indentation, never tabs.

For import statements, follow this exact order: React imports first, then
Next.js imports, then third-party modules, then internal types (@/types), then
internal libraries (@/lib), then components (@/components), then app code
(@/app), then other internal modules (@/), finally relative imports (./).

## Architecture and Dependencies

Never create circular dependencies between modules. Follow clear dependency
direction - components can use utilities, but utilities cannot import
components. Production code must never import from test files or
devDependencies.

## Type Safety Guidelines

### Always Define React Component Types

Every React component must have explicit type definitions. Import React and
define proper interfaces for all props.

```typescript
// DO: Explicit React imports and type definitions
import React from 'react';
import type { ReactNode, FC } from 'react';

interface ComponentProps {
  children: ReactNode;
  title: string;
}

const Component: FC<ComponentProps> = ({ children, title }) => {
  return <div>{title}: {children}</div>;
};
```

### Never Use Any Types

Always use specific TypeScript types instead of 'any'. Use interfaces, unions,
or generics for better type safety.

```typescript
// DO: Use specific types or generics
interface ApiResponse<T> {
  data: T;
  status: number;
  message: string;
}

function processData<T>(input: T): T {
  return input;
}

// DON'T: Use any type
function processData(input: any): any {
  return input;
}
```

### Handle Null and Undefined Values

Always check for null and undefined values. Use optional chaining and nullish
coalescing operators.

```typescript
// DO: Handle null/undefined cases
function getContentTitle(content: Content | null): string {
  if (!content) {
    return 'Untitled';
  }
  return content.title ?? 'No Title';
}

const title = content?.metadata?.title ?? 'Default Title';
```

## Code Complexity Guidelines

### Keep Functions Under 80 Lines

Split large functions into smaller, focused functions. Each function should have
a single responsibility.

```typescript
// DO: Split functions, single responsibility
function validateContentMetadata(metadata: ContentMetadata): ValidationResult {
  const titleValidation = validateTitle(metadata.title);
  const authorValidation = validateAuthor(metadata.author);
  const dateValidation = validateDate(metadata.date);

  return combineValidationResults([
    titleValidation,
    authorValidation,
    dateValidation
  ]);
}

function validateTitle(title: string): FieldValidation {
  if (!title || title.trim().length === 0) {
    return { field: 'title', valid: false, error: 'Title is required' };
  }
  if (title.length > 100) {
    return { field: 'title', valid: false, error: 'Title too long' };
  }
  return { field: 'title', valid: true };
}
```

### Limit Cyclomatic Complexity to 10

Use strategy patterns and lookup tables instead of complex if-else chains. Keep
decision logic simple.

```typescript
// DO: Use strategy pattern to reduce complexity
const contentProcessors = {
  markdown: processMarkdownContent,
  mdx: processMdxContent,
  html: processHtmlContent
} as const;

function processContent(content: Content): ProcessedContent {
  const processor = contentProcessors[content.type];
  if (!processor) {
    throw new Error(`Unsupported content type: ${content.type}`);
  }
  return processor(content);
}
```

### Avoid Deep Nesting Beyond 4 Levels

Use early returns and guard clauses to reduce nesting depth. This makes code
more readable.

```typescript
// DO: Early returns to reduce nesting
function processUserContent(user: User, content: Content): ProcessResult {
  if (!user.isActive) {
    return { success: false, error: 'User inactive' };
  }

  if (!content.isValid) {
    return { success: false, error: 'Invalid content' };
  }

  if (!user.hasPermission(content.type)) {
    return { success: false, error: 'No permission' };
  }

  return processValidContent(user, content);
}
```

## Security Guidelines

### Prevent Object Injection Attacks

Never use user input directly as object property keys. Always validate property
names against a whitelist.

```typescript
// DO: Use whitelist validation
const allowedProperties = new Set(['title', 'author', 'date', 'content']);

function getContentProperty(content: Content, property: string): unknown {
  if (!allowedProperties.has(property)) {
    throw new Error(`Property ${property} is not allowed`);
  }
  return content[property as keyof Content];
}

// DO: Use type-safe property access
function getContentProperty<K extends keyof Content>(
  content: Content,
  property: K
): Content[K] {
  return content[property];
}
```

### Secure File System Access

Always validate and normalize file paths. Prevent directory traversal attacks
and restrict file access to allowed directories.

```typescript
// DO: Path validation and normalization
import path from 'path';

const CONTENT_DIR = path.join(process.cwd(), 'content');
const ALLOWED_EXTENSIONS = ['.md', '.mdx'];

function readContentFile(filename: string): string {
  if (!filename || typeof filename !== 'string') {
    throw new Error('Invalid filename');
  }

  const normalizedPath = path.normalize(filename);
  if (normalizedPath.includes('..')) {
    throw new Error('Path traversal detected');
  }

  const ext = path.extname(normalizedPath);
  if (!ALLOWED_EXTENSIONS.includes(ext)) {
    throw new Error(`Unsupported file extension: ${ext}`);
  }

  const fullPath = path.join(CONTENT_DIR, normalizedPath);
  if (!fullPath.startsWith(CONTENT_DIR)) {
    throw new Error('File outside allowed directory');
  }

  return fs.readFileSync(fullPath, 'utf-8');
}
```

### Validate All User Inputs

Use schema validation for all user inputs. Never trust data from external
sources without validation.

```typescript
// DO: Validate all user inputs
function createContent(input: unknown): Content {
  const schema = z.object({
    title: z.string().min(1).max(100),
    content: z.string().min(1),
    author: z.string().min(1),
    tags: z.array(z.string()).optional()
  });

  const validated = schema.parse(input);
  return new Content(validated);
}
```

### Prevent XSS Attacks

Never use dangerouslySetInnerHTML in React components. If HTML rendering is
required, use a trusted sanitization library like DOMPurify to clean the content
first.

```typescript
// DON'T: Never use dangerouslySetInnerHTML without sanitization
function UnsafeComponent({ htmlContent }: { htmlContent: string }) {
  return <div dangerouslySetInnerHTML={{ __html: htmlContent }} />; // ❌ XSS risk
}

// DO: Use trusted sanitization library
import DOMPurify from 'dompurify';

function SafeComponent({ htmlContent }: { htmlContent: string }) {
  const sanitizedHTML = DOMPurify.sanitize(htmlContent);
  return <div dangerouslySetInnerHTML={{ __html: sanitizedHTML }} />;
}

// DO: Prefer safe alternatives when possible
function PreferredComponent({ textContent }: { textContent: string }) {
  return <div>{textContent}</div>; // Automatically escaped by React
}
```

## Code Quality Guidelines

### Use Structured Logging Instead of Console

Never use console.log, console.error, or console.warn in production code. Use a
proper logging system with structured data.

```typescript
// DO: Use structured logging
import { logger } from '@/lib/logger';

function processContent(content: Content): ProcessedContent {
  logger.info('Processing content', {
    contentId: content.id,
    type: content.type
  });

  try {
    const result = transform(content);
    logger.debug('Content processed successfully', {
      contentId: content.id,
      resultSize: result.size
    });
    return result;
  } catch (error) {
    logger.error('Content processing failed', {
      contentId: content.id,
      error: error.message
    });
    throw error;
  }
}
```

### Prefer Dot Notation for Property Access

Use dot notation when accessing object properties. Only use bracket notation for
dynamic properties or special characters.

```typescript
// DO: Use dot notation for property access
const title = content.metadata.title;
const author = content.metadata.author;

// Exception: Environment variables
const nodeEnv = process.env['NODE_ENV'];
const apiKey = process.env['API_KEY'];
```

### Define Constants Instead of Magic Numbers

Replace magic numbers with named constants. This makes code more readable and
maintainable. Allowed exceptions: 0, 1, -1 (common loop values), 100
(percentages), standard HTTP status codes (200, 404, 500, etc.).

```typescript
// DO: Define constants for business logic numbers
const MAX_TITLE_LENGTH = 100;
const DEFAULT_PAGE_SIZE = 20;
const CACHE_TTL_SECONDS = 3600;

function validateTitle(title: string): boolean {
  return title.length <= MAX_TITLE_LENGTH;
}

// DO: Allowed exceptions - no constants needed
const items = array.slice(0, 1); // 0, 1, -1 are allowed
const percentage = (value / total) * 100; // 100 for percentage is allowed
const isSuccess = response.status === 200; // HTTP status codes are allowed
const hasItems = items.length > 0; // 0 comparison is allowed

// DON'T: Use magic numbers for business logic
const isValidAge = age > 18; // ❌ Should be const MINIMUM_AGE = 18
```

### Remove or Mark Unused Variables

Remove unused variables or prefix them with underscore if they're intentionally
unused for interface compatibility. All declared variables and function
parameters must be used, or explicitly marked as intentionally unused.

```typescript
// DO: Remove unused variables or prefix with underscore
function processContent(content: Content, _metadata?: Metadata): ProcessedContent {
  // _metadata is intentionally unused but kept for interface compatibility
  return transform(content);
}

// DO: Remove truly unused variables
function calculateTotal(items: Item[]): number {
  // const unusedVar = 'remove this'; // ❌ Remove completely
  return items.reduce((sum, item) => sum + item.price, 0);
}

// DO: Use underscore for intentionally unused parameters
function handleEvent(_event: Event, data: EventData): void {
  // _event parameter required by interface but not used in this implementation
  processEventData(data);
}
```

## Performance Guidelines

### Respect Bundle Size Limits

Keep bundle sizes within these limits to ensure fast loading times:

- Main App Bundle: ≤ 50 KB
- Framework Bundle: ≤ 130 KB
- Shared Chunks: ≤ 260 KB
- CSS Bundle: ≤ 50 KB

### Minimize Code Duplication

Keep code duplication under 3%. Extract common functionality into reusable
utilities.

```typescript
// DO: Extract common functionality
function createValidator<T>(schema: z.ZodSchema<T>) {
  return (input: unknown): T => {
    try {
      return schema.parse(input);
    } catch (error) {
      logger.error('Validation failed', { error });
      throw new ValidationError('Invalid input');
    }
  };
}

const contentValidator = createValidator(contentSchema);
const userValidator = createValidator(userSchema);
```

## Quality Standards

### Code Complexity Limits

- Cyclomatic Complexity: ≤ 10
- Function Length: ≤ 80 lines
- Nesting Depth: ≤ 4 levels
- Parameter Count: ≤ 5 parameters
- File Length: ≤ 500 lines

### Type Safety Requirements

- No implicit any types allowed
- No unused variables or parameters
- 100% type coverage required

### Security Standards

- Zero tolerance for injection vulnerabilities
- All file system access must be validated
- No XSS vulnerabilities allowed
- All user inputs must be sanitized

### Cryptographic Security Requirements

Use only approved cryptographic algorithms and secure random number generation.
For encryption use AES-256, for hashing use SHA-256 or stronger, and use
crypto.randomBytes() for secure random generation.

```typescript
// DO: Use approved cryptographic algorithms
import crypto from 'crypto';

// Secure encryption
const algorithm = 'aes-256-gcm';
const key = crypto.randomBytes(32); // 256-bit key
const iv = crypto.randomBytes(16);

function encryptData(text: string, key: Buffer): EncryptedData {
  const cipher = crypto.createCipher(algorithm, key);
  // Implementation with proper IV and authentication tag
}

// Secure hashing
function hashPassword(password: string, salt: string): string {
  return crypto.pbkdf2Sync(password, salt, 100000, 64, 'sha256').toString('hex');
}

// Secure random generation
function generateSecureToken(): string {
  return crypto.randomBytes(32).toString('hex');
}

// DON'T: Use weak algorithms
const weakHash = crypto.createHash('md5'); // ❌ MD5 is cryptographically broken
const insecureRandom = Math.random(); // ❌ Not cryptographically secure
```

## React and Next.js Best Practices

### React Hooks Rules

Follow the Rules of Hooks - only call hooks at the top level of React functions,
never inside loops, conditions, or nested functions. The dependency array of
useEffect, useCallback, and useMemo must be exhaustive and include all values
from component scope that are used inside the effect.

### Component Standards

Use PascalCase for React component names. Always define explicit prop
interfaces. Use React Server Components by default in Next.js 15, only use 'use
client' when interactivity is required.

### Next.js Patterns

Use the App Router for all new pages. Implement proper metadata generation for
SEO. Use next/image for all images instead of standard img tags. Use dynamic
imports for code splitting when components are large or conditionally rendered.
